name: 🐛 Bug report
description: Something isn't working as expected? Here is the right place to report.
labels: ["bug"]

body:
  - type: markdown
    attributes:
      value: Please fill out each section below. This info allows maintainers to diagnose (and fix!) your issue as quickly as possible. Before opening a new issue, please search existing issues [here](https://github.com/saadpasta/developerFolio/issues).

  - type: textarea
    id: description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is
    validations:
      required: true

  - type: textarea
    id: reproduce
    attributes:
      label: Steps to reproduce
      description: Steps to reproduce the behavior
      value: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true

  - type: input
    attributes:
      label: Is this responsiveness Issue
      description: Write YES/NO in the box below
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: Please add screenshots if applicable
    validations:
      required: false

  - type: textarea
    attributes:
      label: Desktop
      description: Please complete the following information
      value: |
        - OS: [e.g. iOS]
        - Browser: [e.g. chrome, safari]
        - Version: [e.g. 22]
    validations:
      required: false

  - type: textarea
    attributes:
      label: Smartphones
      description: Please complete the following information
      value: |
        - Device: [e.g. iPhone6]
        - OS: [e.g. iOS8.1]
        - Browser [e.g. stock browser, safari]
        - Version [e.g. 22]
    validations:
      required: false

  - type: textarea
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
    validations:
      required: false
