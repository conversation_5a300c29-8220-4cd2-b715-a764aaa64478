name: Check Format
on:
  push:
  pull_request:
      branches:
        - master
jobs:
  check-format:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout 🛎️
        uses: actions/checkout@v2
        with:
          persist-credentials: false

      - uses: actions/setup-node@v2-beta
        with:
          node-version: '12'

      - name: Install Prettier
        run: npm install -g prettier

      - name: Check Format
        run: |
          prettier -c "./**/*.{js,css,json}"

