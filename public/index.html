<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- Edit meta tags for SEO -->
    <title>Aiman</title>
    <meta name="title" content="Aiman PortFolio">
    <meta name="description" content="Software Developer Portfolio Template that helps you showcase your work and skills as a software developer">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://developerfolio.js.org/">
    <meta property="og:title" content="DeveloperFolio | Developer Portfolio Template">
    <meta property="og:description" content="Software Developer Portfolio Template that helps you showcase your work and skills as a software developer">
    <!-- Link to a hosted image -->
    <meta property="og:image" content="">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://developerfolio.js.org/">
    <meta property="twitter:title" content="DeveloperFolio | Developer Portfolio Template">
    <meta property="twitter:description" content="Software Developer Portfolio Template that helps you showcase your work and skills as a software developer">
    <!-- Link to a hosted image -->
    <!-- <meta property="twitter:image" content=""> -->

    <meta name="msapplication-TileColor" content="#603cba">
    <meta name="theme-color" content="#6c63ff" />

    <!-- Customize icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="public\apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="16x16" href="public\apple-touch-icon.png">
    <link rel="mask-icon" href="%PUBLIC_URL%/safari-pinned-tab.svg?v8=qAJ44G5Bm7" color="#885bd5">
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico?v8=qAJ44G5Bm7">
    <link rel="apple-touch-icon" sizes="180x180" href="public\apple-touch-icon.png">
	  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <link rel=preload href="/static/media/Montserrat-Regular.ee653992.ttf" as="font" type="font/woff" crossorigin>
    <link rel=preload href="/static/media/Agustina.21f233e1.woff" as="font" type="font/woff" crossorigin>
    
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-*********-2"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'UA-*********-2');
    </script>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/FortAwesome/Font-Awesome@5.15.4/css/all.min.css">


    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
