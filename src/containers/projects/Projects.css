.projects-header {
  text-align: center;
  margin-bottom: 2rem;
}

.projects-header-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--text-color);
}

.projects-header-subtitle {
  font-size: 1.2rem;
  color: var(--text-color-secondary);
}

.projects-main-div {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem;
}

.project-card {
  background: var(--card-background);
  border-radius: 10px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.project-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.project-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

.project-link:hover {
  color: var(--primary-color-dark);
}

.project-description {
  color: var(--text-color-secondary);
  line-height: 1.6;
}

@media (max-width: 768px) {
  .projects-main-div {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .projects-header-title {
    font-size: 2rem;
  }
} 